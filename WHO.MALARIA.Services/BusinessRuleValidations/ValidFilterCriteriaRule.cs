﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Services.BusinessRuleValidations
{
    public class ValidFilterCriteriaRule : IBusinessRule
    {
        private readonly List<FilterCriteria> _filterCriterias;
        private string _validationMessage = "Filter criteria is not valid.";

        // SECURITY FIX: Whitelist of allowed field names (synchronized with CommandGenerator)
        private static readonly HashSet<string> AllowedFields = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Id", "Email", "Username", "Name", "Status", "DisplayStatus", "CaseStrategyType",
            "Role", "StartDate", "EndDate", "Country", "CountryId", "UserType", "IsActive",
            "CreatedAt", "UpdatedAt", "OrganizationName", "Language", "Theme", "Locked",
            "LastLoginOn", "IsDeactivated", "ChangePassword", "PasswordChangedOn",
            "ShouldChangePassword", "TwoFactorEnabled", "Mode", "LockoutEnd",
            "AccessFailedCount", "AuthenticatorKey", "RecoveryCodes",
            "UpdatePasswordLinkValidUntil", "UpdatePasswordLink", "PasswordHash",
            "Approach", "CanConfigure", "CanViewDetails"
        };

        // SECURITY FIX: Whitelist of allowed operators (synchronized with CommandGenerator)
        private static readonly HashSet<string> AllowedOperators = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "eq", "contains", "=", "!=", "<>", ">", "<", ">=", "<=", "LIKE"
        };

        public ValidFilterCriteriaRule(List<FilterCriteria> filterCriterias)
        {
            _filterCriterias = filterCriterias;
        }

        public string Message => _validationMessage;

        public bool IsBroken()
        {
            if (_filterCriterias == null || !_filterCriterias.Any())
                return false;

            foreach (var criteria in _filterCriterias)
            {
                // SECURITY FIX: Enhanced validation with detailed error messages
                if (CheckNullOrEmpty(criteria.Field, criteria.Operator))
                {
                    _validationMessage = "Filter criteria field and operator cannot be null or empty.";
                    return true;
                }

                // SECURITY FIX: Check field name against whitelist
                if (!AllowedFields.Contains(criteria.Field.Trim()))
                {
                    _validationMessage = $"Field name '{criteria.Field}' is not allowed for security reasons.";
                    return true;
                }

                // SECURITY FIX: Check operator against whitelist
                if (!AllowedOperators.Contains(criteria.Operator.Trim()))
                {
                    _validationMessage = $"Operator '{criteria.Operator}' is not allowed for security reasons.";
                    return true;
                }

                // SECURITY FIX: Check for SQL injection patterns
                if (ContainsSqlInjectionPatterns(criteria.Field) || ContainsSqlInjectionPatterns(criteria.Operator))
                {
                    _validationMessage = "Invalid characters detected in filter criteria that could pose security risks.";
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// CheckNullOrEmpty for filter criteria
        /// </summary>
        /// <param name="field">An entity attribute(field)</param>
        /// <param name="sqlOperator">A Sql operator</param>
        /// <returns></returns>
        private bool CheckNullOrEmpty(string field, string sqlOperator)
        {
            return string.IsNullOrWhiteSpace(field) || string.IsNullOrWhiteSpace(sqlOperator);
        }

        /// <summary>
        /// SECURITY FIX: Check for common SQL injection patterns
        /// </summary>
        /// <param name="input">Input string to check</param>
        /// <returns>True if SQL injection patterns are detected</returns>
        private static bool ContainsSqlInjectionPatterns(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            // Common SQL injection patterns
            var sqlInjectionPatterns = new[]
            {
                @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|MERGE|SELECT|UPDATE|UNION|USE)\b)",
                @"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
                @"(\b(OR|AND)\s+['""]?\w+['""]?\s*=\s*['""]?\w+['""]?)",
                @"(--|#|/\*|\*/)",
                @"(\bxp_\w+)",
                @"(\bsp_\w+)",
                @"(\b(WAITFOR|DELAY)\b)",
                @"(;|\x00)",
                @"(\b(CAST|CONVERT|CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()",
                @"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
            };

            return sqlInjectionPatterns.Any(pattern =>
                Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline));
        }

        public int StatusCode => (int)HttpStatusCode.PreconditionFailed;
    }
}

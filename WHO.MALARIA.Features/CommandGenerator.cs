﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Features
{
    public static class CommandGenerator
    {
        // SECURITY FIX: Whitelist of allowed field names to prevent SQL injection
        private static readonly HashSet<string> AllowedFields = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Id", "Email", "Username", "Name", "Status", "DisplayStatus", "CaseStrategyType",
            "Role", "StartDate", "EndDate", "Country", "CountryId", "UserType", "IsActive",
            "CreatedAt", "UpdatedAt", "OrganizationName", "Language", "Theme", "Locked",
            "LastLoginOn", "IsDeactivated", "ChangePassword", "PasswordChangedOn",
            "ShouldChangePassword", "TwoFactorEnabled", "Mode", "LockoutEnd",
            "AccessFailedCount", "Authenticator<PERSON><PERSON>", "RecoveryCodes",
            "UpdatePasswordLinkValidUntil", "UpdatePasswordLink", "PasswordHash",
            "Approach", "CanConfigure", "CanViewDetails"
        };

        // SECURITY FIX: Whitelist of allowed operators to prevent SQL injection
        private static readonly HashSet<string> AllowedOperators = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "eq", "contains", "=", "!=", "<>", ">", "<", ">=", "<=", "LIKE"
        };

        /// <summary>
        /// Build the sql Parameter to be passed in a query - SECURITY HARDENED VERSION
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria </param>
        /// <returns>string of filter criteria</returns>
        /// <exception cref="ArgumentException">Thrown when invalid field names or operators are detected</exception>
        public static string BuildWhereClauseParameters(List<FilterCriteria> filterCriterias)
        {
            if (filterCriterias == null || !filterCriterias.Any())
                return string.Empty;

            // SECURITY FIX: Validate all filter criteria before processing
            ValidateFilterCriterias(filterCriterias);

            var criterias = filterCriterias.Select(field => $"{SanitizeFieldName(field.Field)} {BuildOperator(field.Operator, field.Field)}").ToList();

            return string.Join(" AND ", criterias);
        }

        /// <summary>
        /// SECURITY FIX: Validate filter criterias against whitelists
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria to validate</param>
        /// <exception cref="ArgumentException">Thrown when invalid field names or operators are detected</exception>
        private static void ValidateFilterCriterias(List<FilterCriteria> filterCriterias)
        {
            foreach (var criteria in filterCriterias)
            {
                if (string.IsNullOrWhiteSpace(criteria.Field))
                    throw new ArgumentException("Field name cannot be null or empty");

                if (string.IsNullOrWhiteSpace(criteria.Operator))
                    throw new ArgumentException("Operator cannot be null or empty");

                // SECURITY FIX: Check field name against whitelist
                if (!AllowedFields.Contains(criteria.Field.Trim()))
                    throw new ArgumentException($"Field name '{criteria.Field}' is not allowed");

                // SECURITY FIX: Check operator against whitelist
                if (!AllowedOperators.Contains(criteria.Operator.Trim()))
                    throw new ArgumentException($"Operator '{criteria.Operator}' is not allowed");

                // SECURITY FIX: Additional validation for SQL injection patterns
                if (ContainsSqlInjectionPatterns(criteria.Field) || ContainsSqlInjectionPatterns(criteria.Operator))
                    throw new ArgumentException("Invalid characters detected in filter criteria");
            }
        }

        /// <summary>
        /// SECURITY FIX: Check for common SQL injection patterns
        /// </summary>
        /// <param name="input">Input string to check</param>
        /// <returns>True if SQL injection patterns are detected</returns>
        private static bool ContainsSqlInjectionPatterns(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            // Common SQL injection patterns
            var sqlInjectionPatterns = new[]
            {
                @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|MERGE|SELECT|UPDATE|UNION|USE)\b)",
                @"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
                @"(\b(OR|AND)\s+['""]?\w+['""]?\s*=\s*['""]?\w+['""]?)",
                @"(--|#|/\*|\*/)",
                @"(\bxp_\w+)",
                @"(\bsp_\w+)",
                @"(\b(WAITFOR|DELAY)\b)",
                @"(;|\x00)",
                @"(\b(CAST|CONVERT|CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()",
                @"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
            };

            return sqlInjectionPatterns.Any(pattern =>
                Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline));
        }

        /// <summary>
        /// SECURITY FIX: Sanitize field name to prevent injection
        /// </summary>
        /// <param name="fieldName">Field name to sanitize</param>
        /// <returns>Sanitized field name</returns>
        private static string SanitizeFieldName(string fieldName)
        {
            if (string.IsNullOrWhiteSpace(fieldName))
                throw new ArgumentException("Field name cannot be null or empty");

            // Remove any potentially dangerous characters and normalize
            var sanitized = fieldName.Trim().Replace(".", "_");

            // Ensure it's still in our whitelist after sanitization
            if (!AllowedFields.Contains(sanitized))
                throw new ArgumentException($"Sanitized field name '{sanitized}' is not in allowed list");

            return sanitized;
        }

        /// <summary>
        ///  Build the operator - SECURITY HARDENED VERSION
        /// </summary>
        /// <param name="_operator">operator as string</param>
        /// <param name="field">field (column) as a string</param>
        /// <returns>SQL formatted operator</returns>
        private static string BuildOperator(string _operator, string field)
        {
            var sanitizedField = SanitizeFieldName(field);
            var normalizedOperator = _operator.Trim().ToLowerInvariant();

            return normalizedOperator switch
            {
                "eq" or "=" => $"= @{sanitizedField}",
                "contains" or "like" => $"LIKE @{sanitizedField}",
                "!=" or "<>" => $"<> @{sanitizedField}",
                ">" => $"> @{sanitizedField}",
                "<" => $"< @{sanitizedField}",
                ">=" => $">= @{sanitizedField}",
                "<=" => $"<= @{sanitizedField}",
                _ => throw new ArgumentException($"Operator '{_operator}' is not supported")
            };
        }


        /// <summary>
        /// Method to calcute file size 
        /// </summary>
        /// <param name="len">file size</param>
        /// <returns>string</returns>
        public static string CalcuateFileSize(double len)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            // Adjust the format string to your preferences. For example "{0:0.#}{1}" would
            // show a single decimal place, and no space.
            return String.Format("{0:0.##} {1}", len, sizes[order]);
        }
    }
}

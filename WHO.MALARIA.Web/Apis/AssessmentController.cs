﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using MediatR;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using Newtonsoft.Json.Linq;

using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AssessmentController : BaseApiController
    {
        private readonly IAssessmentQueries _assessmentQueries;
        private readonly IDeskReviewQueries _deskReviewQueries;

        public AssessmentController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IAssessmentQueries assessmentQueries, IDeskReviewQueries deskReviewQueries) : base(mediator, httpContextAccessor)
        {
            _assessmentQueries = assessmentQueries;
            _deskReviewQueries = deskReviewQueries;
        }

        #region Assessment
        /// <summary>
        /// Get paginated list of assessments according to filter/sort criterias
        /// SECURITY FIX: Enhanced with proper input validation
        /// </summary>
        /// <param name="inputDto">Input base object for list returning</param>
        /// <returns>List of assessments</returns>
        [HttpPost]
        [Route("assessments")]
        public async Task<ActionResult<QueryListResultDto<AssessmentListResultDto>>> GetAssessmentsAsync(InputDtoBase inputDto)
        {
            try
            {
                if (inputDto == null)
                    return BadRequest("Request data is required");

                // SECURITY FIX: Limit the number of filter criteria to prevent abuse
                if (inputDto.FilterCriterias != null && inputDto.FilterCriterias.Count > 15)
                {
                    return BadRequest("Too many filter criteria. Maximum 15 allowed.");
                }

                // SECURITY FIX: Validate pagination parameters
                if (inputDto.Take > 1000)
                {
                    return BadRequest("Maximum page size is 1000 records");
                }

                if (inputDto.Skip < 0 || inputDto.Take <= 0)
                {
                    return BadRequest("Invalid pagination parameters");
                }

                inputDto.CurrentUserId = base.GetCurrentUser().UserId;
                UserRoleEnum userType = (UserRoleEnum)Convert.ToInt32(base.GetCurrentUser().UserType);

                return await _assessmentQueries.GetAssessmentsAsync(inputDto, userType);
            }
            catch (ArgumentException ex)
            {
                // SECURITY FIX: Return generic error message to prevent information disclosure
                return BadRequest("Invalid request parameters provided");
            }
            catch (Exception)
            {
                // SECURITY FIX: Log the actual error but return generic message
                return StatusCode(500, "An error occurred while processing the request");
            }
        }

        private int UserRoleEnum(string userType)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Get assessment using assessment id
        /// </summary>
        /// <param name="assessmentId">Id of assessment</param>
        /// <returns> Assessment object </returns>
        [HttpGet]
        [Route("{assessmentId}")]
        public async Task<ActionResult<GetAssessmentOutputDto>> GetAssessmentAsync(Guid assessmentId)
        {
            if (assessmentId != Guid.Empty)
            {
                return await _assessmentQueries.GetAssessmentAsync(assessmentId, base.GetCurrentUser().UserId);
            }

            return NoContent();
        }

        /// <summary>
        /// Creates new assessment
        /// </summary>
        /// <param name="command">An object of CreateAssessmentCommand</param>
        /// <returns> Assessment Id </returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("create")]
        public async Task<ActionResult<Guid>> CreateAssessmentAsync([FromBody] CreateAssessmentCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            Guid assessmentId = await CommandAsync(command);
            return Ok(assessmentId);
        }

        /// <summary>
        /// Update existing assessment
        /// </summary>
        /// <param name="command"> An object of UpdateAssessmentCommand </param>
        /// <returns> Assessment Id </returns>
        [HttpPut]
        [Route("update")]
        public async Task<ActionResult<Guid>> UpdateAssessmentConfigurationAsync([FromBody] UpdateAssessmentConfigurationCommand command)
        {
            if (_assessmentQueries.DoesAssessmentExist(command.AssessmentId))
            {
                command.CurrentUserId = base.GetCurrentUser().UserId;
                Guid assessmentId = await CommandAsync(command);
                return Ok(assessmentId);
            }
            return NotFound();
        }

        /// <summary>
        /// Set assessment status as finalized
        /// </summary>
        /// <param name="command"></param>
        /// <returns>retruns true if status set successfully</returns>
        [HttpPost]
        [Route("finalize")]
        public async Task<ActionResult<bool>> FinalizeAssessment([FromBody] FinalizeAssessmentCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Returns user permissions on assessment
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <returns>UserAssessmentPermissionDto object</returns>
        [HttpGet]
        [Route("permission/{assessmentId}")]
        [ProducesResponseType(typeof(UserAssessmentPermissionDto), 200)]
        [ProducesResponseType(401)]
        public async Task<ActionResult<UserAssessmentPermissionDto>> GetUserPermissionsOnAssessmentAsync(Guid assessmentId)
        {
            return Ok(await _assessmentQueries.GetUserPermissionsOnAssessmentAsync(assessmentId, base.GetCurrentUser().UserId));
        }
        #endregion

        #region Objective
        /// <summary>
        /// Get all objectives
        /// </summary>
        /// <returns>List of objectives</returns>
        [HttpGet]
        [Route("objectives")]
        public async Task<ActionResult<IEnumerable<ObjectiveDto>>> GetAllObjectivesAsync()
        {
            return Ok(await _assessmentQueries.GetAllObjectivesAsync());
        }

        /// <summary>
        /// Get sub-objectives for an objective
        /// </summary>
        /// <param name="objectiveId">Objective Id for which sub-objectives have to be loaded</param>
        /// <returns>List of sub objectives</returns>
        [HttpGet]
        [Route("subObjectives/{objectiveId}")]
        public async Task<ActionResult<IEnumerable<SubObjectiveDto>>> GetSubObjectivesAsync(Guid objectiveId)
        {
            if (objectiveId != Guid.Empty)
            {
                return Ok(await _assessmentQueries.GetSubObjectivesAsync(objectiveId));
            }

            return NoContent();
        }

        /// <summary>
        /// Get indicators for a sub-objective
        /// </summary>
        /// <param name="subObjectiveId">SubObjective Id for which indicators have to be loaded</param>
        /// <returns>List of indicators</returns>
        [HttpGet]
        [Route("subObjective/indicators/{subObjectiveId}")]
        public async Task<ActionResult<IEnumerable<IndicatorDto>>> GetSubObjectiveIndicatorsAsync(Guid subObjectiveId)
        {
            if (subObjectiveId != Guid.Empty)
            {
                return Ok(await _assessmentQueries.GetSubObjectiveIndicatorsAsync(subObjectiveId));
            }

            return NoContent();
        }

        /// <summary>
        /// Get uploaded objective diagrams
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>
        /// <returns>Digrams and its other details</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/{assessmentId}/{strategyId}/objective-diagram")]
        public async Task<ActionResult> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            ObjectiveDiagramDto objectiveDiagram = await _assessmentQueries.GetObjectiveDiagramsAsync(assessmentId, strategyId);
            return Ok(objectiveDiagram);
        }

        /// <summary>
        /// Get uploaded sub objective diagrams
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>
        /// <returns>Sub objective digrams</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/{assessmentId}/{strategyId}/sub-objective-diagram")]
        public async Task<ActionResult> GetSubObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            IEnumerable<SubObjectiveDiagramDto> diagrams = await _assessmentQueries.GetSubObjectiveDiagramsAsync(assessmentId, strategyId);
            return Ok(diagrams);
        }

        /// <summary>
        /// Upload objective diagram and save other details
        /// </summary>
        /// <param name="command">Object of UploadObjectiveDiagramCommand class</param>
        /// <returns>An array of diagram files ids</returns>
        [HttpPost]
        [Route("dataCollect/deskReview/objective-diagram/upload")]
        public async Task<ActionResult> UploadObjectiveDiagramAsync([FromForm] UploadObjectiveDiagramCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Upload sub objective diagram and save other details
        /// </summary>
        /// <param name="command">Object of UploadSubObjectiveDiagramCommand class</param>
        /// <returns>An array of diagram files ids</returns>
        [HttpPost]
        [Route("dataCollect/deskReview/sub-objective-diagram/upload")]
        public async Task<ActionResult> UploadSubObjectiveDiagramAsync([FromForm] UploadSubObjectiveDiagramCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            return Ok(await CommandAsync(command));
        }



        /// <summary>
        /// Get objective and subObjective diagram status based on the assessmentId and strategyId
        /// </summary>
        /// <param name="assessmentId">Assessment id for which the diagram has been uploaded</param>
        /// <param name="strategyId">Strategy id for which the diagram has been uploaded</param>
        /// <returns>return status of objective and subObjective diagram</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/{assessmentId}/{strategyId}/diagram-status")]
        public async Task<ActionResult> GetDiagramsStatusAsync(Guid assessmentId, Guid strategyId)
        {
            DiagramsStatusDto diagramsStatus = await _assessmentQueries.GetDiagramsStatusAsync(assessmentId, strategyId);
            return Ok(diagramsStatus);
        }

        #endregion

        #region Strategy
        /// <summary>
        /// Get all strategies
        /// </summary>
        /// <returns>List of strategies</returns>
        [HttpGet]
        [Route("strategies")]
        public async Task<ActionResult<IEnumerable<StrategyDto>>> GetAllStrategiesAsync()
        {
            return Ok(await _assessmentQueries.GetAllStrategiesAsync());
        }

        /// <summary>
        /// Get selected strategies for an assessment
        /// </summary>
        /// <param name="assessmentId">Assessment Id for which indicators have to be loaded</param>
        /// <returns>List of strategies</returns>
        [HttpGet]
        [Route("strategies/{assessmentId}")]
        public async Task<ActionResult<IEnumerable<StrategyDto>>> GetAssessmentStrategiesAsync(Guid assessmentId)
        {
            if (assessmentId != Guid.Empty)
            {
                return Ok(await _assessmentQueries.GetAssessmentStrategiesAsync(assessmentId));
            }

            return NoContent();
        }

        /// <summary>
        /// Creates or Updates assessment strategies
        /// </summary>
        /// <param name="command">CreateOrUpdateAssessmentStrategiesCommand object</param>
        /// <returns>Returns true if strategies are updated successfully else false</returns>
        [HttpPost]
        [Route("saveStrategies")]
        public async Task<ActionResult<bool>> SaveAssessmentStrategiesAsync([FromBody] CreateOrUpdateAssessmentStrategiesCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);
            return Ok(isSuccess);
        }
        #endregion

        #region Indicator
        /// <summary>
        /// Get selected assessments for an assessment for scope definition
        /// </summary>
        /// <param name="inputDto">GetAssessmentIndicatorsInputDto object with input values</param>
        /// <returns>List of indicators</returns>
        [HttpGet]
        [Route("scopedefinition/indicators/{assessmentId}")]
        public async Task<ActionResult<IEnumerable<IndicatorDto>>> GetScopeDefinitionAssessmentIndicatorsAsync(Guid assessmentId)
        {
            if (assessmentId != Guid.Empty)
            {
                return Ok(await _assessmentQueries.GetScopeDefinitionAssessmentIndicatorsAsync(assessmentId));
            }

            return NoContent();
        }

        /// <summary>
        /// Get selected assessments for an assessment for data collection
        /// </summary>
        /// <param name="inputDto">GetAssessmentIndicatorsInputDto object with input values</param>
        /// <returns>List of indicators</returns>
        [HttpPost]
        [Route("datacollect/deskreview/indicators")]
        public async Task<ActionResult<IEnumerable<IndicatorDto>>> GetDataCollectAssessmentIndicatorsAsync([FromBody] GetAssessmentIndicatorsInputDto inputDto)
        {
            if (inputDto.AssessmentId != Guid.Empty)
            {
                return Ok(await _assessmentQueries.GetAssessmentIndicatorsForDeskReviewAsync(inputDto));
            }

            return NoContent();
        }

        /// <summary>
        /// Returns indicators based on the selected strategies
        /// </summary>
        /// <param name="inputDto">Object of GetIndicatorsInputDto</param>
        /// <returns>List of IndicatorDto objects</returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IndicatorDto>), 200)]
        [ProducesResponseType(401)]
        [Route("strategy/indicators")]
        public async Task<ActionResult<IEnumerable<IndicatorDto>>> GetStrategyIndicatorsAsync([FromBody] GetIndicatorsInputDto inputDto)
        {
            return Ok(await _assessmentQueries.GetStrategyIndicatorsAsync(inputDto));
        }

        /// <summary>
        /// Creates or updates assessment indicators
        /// </summary>
        /// <param name="command">CreateOrUpdateAssessmentIndicatorsCommand object</param>
        /// <returns>Returns true if indicators are updated successfully else false</returns>
        [HttpPost]
        [Route("saveIndicators")]
        public async Task<ActionResult<bool>> SaveAssessmentIndicatorsAsync([FromBody] CreateOrUpdateAssessmentIndicatorsCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);
            return Ok(isSuccess);
        }
        #endregion

        #region Desk review response
        /// <summary>
        /// Get desk review response for the specific indicator which is associated with the specific strategy
        /// </summary>
        /// <param name="assessmentIndicatorId">Indicator id associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id associated with the assessment</param>
        /// <returns>JSON object</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/response/{assessmentIndicatorId}/{assessmentStrategyId}")]
        public ActionResult GetDeskReviewResponse(Guid assessmentIndicatorId, Guid assessmentStrategyId)
        {
            JObject response = _assessmentQueries.GetDeskReviewResponse(assessmentIndicatorId, assessmentStrategyId);
            return Ok(response);
        }

        /// <summary>
        /// Get desk review response documents
        /// </summary>
        /// <param name="assessmentIndicatorId">Indicator id that is associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id that is associated with the assessment</param>
        /// <returns>List of documents</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/response/documents/{assessmentIndicatorId}/{assessmentStrategyId}")]
        public async Task<IEnumerable<ResponseDocumentDto>> GetDeskReviewResponseDocumentsAsync(Guid assessmentIndicatorId, Guid assessmentStrategyId) =>
            await _deskReviewQueries.GetResponseDocuments(assessmentIndicatorId, assessmentStrategyId);


        /// <summary>
        /// Add/Update indicator assessment details for the desk review
        /// </summary>
        /// <param name="command">An object of CreateOrUpdateIndicatorAssessmentDeskReviewCommand</param>
        /// <returns>True if operation is successful</returns>
        [HttpPost]
        [Route("dataCollect/deskReview/response/save")]
        public async Task<ActionResult> SaveAssessmentDeskReviewResponseAsync(CreateOrUpdateIndicatorAssessmentDeskReviewCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Save desk review response and uploaded files
        /// </summary>
        /// <param name="command">Input object of CreateOrUpdateIndicatorAssessmentDeskReviewCommand class</param>
        /// <returns>200 HTTP status code if successful else 500 HTTP status code</returns>
        [HttpPost]
        [Route("dataCollect/deskReview/response/save/upload-document")]
        public async Task<ActionResult> SaveDeskReviewResponseAsync([FromForm] CreateOrUpdateIndicatorAssessmentDeskReviewCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Get desk review response for the specific indicator which is associated with the specific strategy
        /// </summary>
        /// <param name="indicatorId">Indicator id associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id associated with the assessment</param>
        /// <returns>JSON object</returns>
        [HttpGet]
        [Route("dataCollect/deskReview/parent/response/{indicatorId}/{assessmentId}/{assessmentStrategyId}")]
        public ActionResult GetDeskReviewResponseForParentIndicator(Guid indicatorId, Guid assessmentId, Guid assessmentStrategyId)
        {
            Guid parentIndicatorId = _assessmentQueries.GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(assessmentId, base.GetParentIndicatorId(indicatorId));

            JObject response = _assessmentQueries.GetDeskReviewResponse(parentIndicatorId, assessmentStrategyId);

            return Ok(response);
        }
        #endregion

        #region Checklist
        /// <summary>
        /// Returns desk review variables based on the strategy where recorded is true
        /// </summary>
        /// <param name="strategyId">Strategy id for which desk review variables are to be fetched</param>
        /// <returns>List of DRVariableCheckListDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<DRVariableCheckListDto>), 200)]
        [ProducesResponseType(401)]
        [Route("datacollect/deskreview/{strategyId}/recordedchecklistvariables")]
        public async Task<ActionResult<IEnumerable<DRVariableCheckListDto>>> GetRecordedVariablesForStrategy(Guid strategyId)
        {
            return Ok(await _assessmentQueries.GetRecordedDRVariablesAsync(strategyId));
        }

        /// <summary>
        /// Returns desk review variables based on the strategy where reported is true
        /// </summary>
        /// <param name="strategyId">Strategy id for which desk review variables are to be fetched</param>
        /// <returns>List of DRVariableCheckListDto objects</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<DRVariableCheckListDto>), 200)]
        [ProducesResponseType(401)]
        [Route("datacollect/deskreview/{strategyId}/reportedchecklistvariables")]
        public async Task<ActionResult<IEnumerable<DRVariableCheckListDto>>> GetReportedVariablesForStrategy(Guid strategyId)
        {
            return Ok(await _assessmentQueries.GetReportedDRVariablesAsync(strategyId));
        }

        /// <summary>
        /// Get desk review indicators checklist for particular strategy
        /// </summary>
        /// <param name="strategyId">Strategy id for which indicators are to be fetched</param>
        /// <returns>List of indicators checklist</returns>
        [HttpGet]
        [Route("datacollect/deskreview/{strategyId}/checklistindicators")]
        public async Task<ActionResult<DRIndicatorCheckListDto>> GetDeskReviewIndicatorsCheckList(Guid strategyId)
        {
            return Ok(await _assessmentQueries.GetDeskReviewIndicatorsChecklistAysnc(strategyId));
        }
        #endregion
    }
}

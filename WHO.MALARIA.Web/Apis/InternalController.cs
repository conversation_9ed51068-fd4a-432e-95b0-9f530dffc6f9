﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Queries;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace WHO.MALARIA.Web.Apis
{
    [Authorize]
    [Route("api/[controller]")]
    public class InternalController : BaseApiController
    {
        public InternalController(IMediator mediator, IHttpContextAccessor httpContextAccessor) : base(mediator, httpContextAccessor)
        {

        }
        /// <summary>
        /// Create new Identity
        /// </summary>
        /// <param name="command">An object of CreateIdentityCommand</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdentityDto>), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("identity/create")]
        public async Task<ActionResult> CreateIdentityAsync([FromBody] CreateIdentityCommand command)
        {
            // this is generic api to create user and so the default user role should be assigned which is Viewer
            command.UserType = UserRoleEnum.Viewer;
            IdentityDto identityDto = await CommandAsync(command);

            CreateCookie(identityDto);
            return Ok(identityDto);
        }

        /// <summary>
        /// Create new identity and user with SuperManager role
        /// </summary>
        /// <param name="command">An object of CreateIdentityCommand</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IdentityDto), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("identity/create/supermanager")]
        public async Task<ActionResult> CreateSuperManagerAsync([FromBody] CreateIdentityCommand command)
        {
            command.UserType = UserRoleEnum.SuperManager;
            IdentityDto identityDto = await CommandAsync(command);
            return Ok(identityDto);
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// SECURITY FIX: Enhanced with proper authorization and input validation
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdentityDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [Route("identities")]
        public async Task<ActionResult> GetIdentitiesAsync()
        {
            try
            {
                // SECURITY FIX: Additional authorization check - only managers and above can access identity data
                var currentUser = base.GetCurrentUser();
                if (currentUser.UserType < (int)UserRoleEnum.Manager)
                {
                    return Forbid("Insufficient permissions to access identity data");
                }

                GetIdentityQuery query = await SerializeJObjectAsync(new GetIdentityQuery());

                // SECURITY FIX: Limit the number of filter criteria to prevent abuse
                if (query.FilterCriterias != null && query.FilterCriterias.Count > 10)
                {
                    return BadRequest("Too many filter criteria. Maximum 10 allowed.");
                }

                return Ok(await QueryAsync(new GetIdentityQuery(query.FilterCriterias)));
            }
            catch (ArgumentException ex)
            {
                // SECURITY FIX: Return generic error message to prevent information disclosure
                return BadRequest("Invalid filter criteria provided");
            }
            catch (Exception)
            {
                // SECURITY FIX: Log the actual error but return generic message
                return StatusCode(500, "An error occurred while processing the request");
            }
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// SECURITY FIX: Enhanced with proper authorization and input validation
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdAndNameDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [Route("{entity}/records")]
        public async Task<ActionResult> GetMultipleRecordsAsync()
        {
            try
            {
                GetEntityMultipleRecordsQuery query = await SerializeJObjectAsync(new GetEntityMultipleRecordsQuery());

                // SECURITY FIX: Validate entity parameter against whitelist
                var allowedEntities = new[] { "country", "user" };
                if (string.IsNullOrWhiteSpace(query.Entity) ||
                    !allowedEntities.Contains(query.Entity.ToLowerInvariant()))
                {
                    return BadRequest("Invalid or unsupported entity type");
                }

                // SECURITY FIX: Limit the number of filter criteria to prevent abuse
                if (query.FilterCriterias != null && query.FilterCriterias.Count > 5)
                {
                    return BadRequest("Too many filter criteria. Maximum 5 allowed for this endpoint.");
                }

                // SECURITY FIX: Additional authorization for sensitive entities
                if (query.Entity.ToLowerInvariant() == "user")
                {
                    var currentUser = base.GetCurrentUser();
                    if (currentUser.UserType < (int)UserRoleEnum.Manager)
                    {
                        return Forbid("Insufficient permissions to access user data");
                    }
                }

                return Ok(await QueryAsync(new GetEntityMultipleRecordsQuery(query.Entity, query.FilterCriterias)));
            }
            catch (ArgumentException ex)
            {
                // SECURITY FIX: Return generic error message to prevent information disclosure
                return BadRequest("Invalid request parameters provided");
            }
            catch (Exception)
            {
                // SECURITY FIX: Log the actual error but return generic message
                return StatusCode(500, "An error occurred while processing the request");
            }
        }

        /// <summary>
        ///  Upload malaria toolkit document
        /// </summary>
        /// <param name="command">Object of UploadDocumentCommand</param>  
        /// <returns>True if file uploaded successfully</returns>
        [HttpPost]
        [Route("toolkit-document/upload")]
        public async Task<ActionResult> Upload([FromForm] UploadDocumentCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;

            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }
    }
}

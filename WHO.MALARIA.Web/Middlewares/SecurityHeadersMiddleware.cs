using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using WHO.MALARIA.Domain.Models;
using System;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Contains the custom headers that need to be added/removed from the each response
    /// </summary>
    public class SecurityHeadersMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly AppSettings _appSettings;

        public SecurityHeadersMiddleware(RequestDelegate next, AppSettings appSettings)
        {
            _next = next;
            _appSettings = appSettings;
        }

        public async Task Invoke(HttpContext context, IWebHostEnvironment env)
        {
            context.Response.Headers.Remove("X-AspNet-Version");
            context.Response.Headers.Append("X-Frame-Options", new StringValues("DENY"));
            context.Response.Headers.Append("Cross-Origin-Opener-Policy", new StringValues("same-origin"));
            context.Response.Headers.Append("Cross-Origin-Embedder-Policy", new StringValues("require-corp"));
            context.Response.Headers.Append("Cross-Origin-Resource-Policy", new StringValues("same-origin"));

            string[] permissionsPolicy = {
                "accelerometer=()", "autoplay=()", "bluetooth=()", "camera=()", "ch-device-memory=()",
                "ch-downlink=()", "ch-dpr=()", "ch-ect=()", "ch-prefers-reduced-motion=()",
                "ch-prefers-reduced-transparency=()", "ch-rtt=()", "ch-save-data=()", "ch-ua-bitness=()",
                "ch-ua-form-factors=()", "ch-ua-full-version=()", "ch-ua-full-version-list=()", "ch-ua-mobile=()",
                "ch-ua-model=()", "ch-ua-platform=()", "ch-ua-platform-version=()", "ch-ua-wow64=()",
                "ch-viewport-height=()", "ch-viewport-width=()", "ch-width=()", "clipboard-read=()",
                "clipboard-write=()", "compute-pressure=()", "cross-origin-isolated=()", "display-capture=()",
                "encrypted-media=()", "fullscreen=()", "geolocation=()", "gyroscope=()", "hid=()",
                "identity-credentials-get=()", "idle-detection=()", "keyboard-map=()", "local-fonts=()",
                "magnetometer=()", "microphone=()", "midi=()", "payment=()", "picture-in-picture=()",
                "publickey-credentials-create=()", "publickey-credentials-get=()", "screen-wake-lock=()",
                "serial=()", "storage-access=()", "sync-xhr=()", "usb=()", "web-share=()", "window-management=()",
                "xr-spatial-tracking=()"
            };

            context.Response.Headers.Append("Permissions-Policy", new StringValues(permissionsPolicy).ToString());
            context.Response.Headers.Append("Strict-Transport-Security", new StringValues("max-age=63072000; includeSubDomains; preload"));
            context.Response.Headers.Append("X-XSS-Protection", new StringValues("0"));
            context.Response.Headers.Append("X-Content-Type-Options", new StringValues("nosniff"));
            context.Response.Headers.Append("Referrer-Policy", new StringValues("no-referrer"));
            context.Response.Headers.Append("Origin-Agent-Cluster", new StringValues("?1"));
            context.Response.Headers.Append("X-DNS-Prefetch-Control", new StringValues("off"));
            context.Response.Headers.Append("X-Robots-Tag", new StringValues("none"));

            if (context.Request.Path.Value.Contains("/api/"))
                context.Response.Headers.Append("Content-Type", new StringValues("application/json; charset=utf-8"));

            string baseUrl = (_appSettings.Origin ?? "https://localhost:5001").TrimEnd('/');

            // Build allowed API endpoints string
            var allowedEndpoints = _appSettings.AllowedApiEndpoints != null && _appSettings.AllowedApiEndpoints.Length > 0
                ? " " + string.Join(" ", _appSettings.AllowedApiEndpoints)
                : "";

            string[] contentSecurityPolicy;

            if (env.IsDevelopment())
            {
                // Development CSP - more permissive for dev tools and any localhost
                var scriptSrc = $"script-src {baseUrl}/scripts/ {baseUrl}/assets/ 'self' 'unsafe-inline' 'unsafe-eval'";
                if (_appSettings.EnableGoogleAnalytics)
                {
                    scriptSrc += " https://www.googletagmanager.com https://www.google-analytics.com";
                }

                contentSecurityPolicy = new string[] {
                    "default-src 'none'",
                    scriptSrc,
                    $"style-src {baseUrl}/assets/ 'self' 'unsafe-inline' https://fonts.googleapis.com",
                    "font-src 'self' https://fonts.gstatic.com",
                    "img-src 'self' blob: data:",
                    // Allow all localhost connections, Azure AD, and configured endpoints for development
                    $"connect-src 'self' wss://localhost:* ws://localhost:* http://localhost:* https://localhost:* https://login.windows.net https://login.microsoftonline.com{allowedEndpoints}",
                    "manifest-src 'self'",
                    "frame-ancestors 'none'",
                    "form-action 'none'",
                    "base-uri 'none'",
                    "upgrade-insecure-requests"
                };
            }
            else
            {
                // Production CSP - strict security with unsafe-inline for Emotion.js
                var scriptSrc = $"script-src {baseUrl}/scripts/ {baseUrl}/assets/ 'self'";
                if (_appSettings.EnableGoogleAnalytics)
                {
                    scriptSrc += " https://www.googletagmanager.com https://www.google-analytics.com";
                }

                contentSecurityPolicy = new string[] {
                    "default-src 'none'",
                    scriptSrc,
                    $"style-src {baseUrl}/assets/ 'self' 'unsafe-inline' https://fonts.googleapis.com",
                    "font-src 'self' https://fonts.gstatic.com",
                    "img-src 'self' blob: data:",
                    $"connect-src 'self' https://login.windows.net https://login.microsoftonline.com{allowedEndpoints}",
                    "manifest-src 'self'",
                    "frame-ancestors 'none'",
                    "form-action 'none'",
                    "base-uri 'none'",
                    "upgrade-insecure-requests"
                };
            }

            context.Response.Headers.Append("Content-Security-Policy",
                new StringValues(contentSecurityPolicy).ToString().Replace(",", "; "));

            await _next.Invoke(context);
        }
    }

    public static class SecurityHeadersMiddlewareExtension
    {
        public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder builder) =>
            builder.UseMiddleware<SecurityHeadersMiddleware>();
    }
}